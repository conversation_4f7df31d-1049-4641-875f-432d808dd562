import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:neorevv_demo/config/responsive.dart';
import 'package:neorevv_demo/screens/auth/login_screen.dart';
import 'package:neorevv_demo/screens/dashboard/dashboard_screen.dart';
import 'package:neorevv_demo/config/constants.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'NeoRevv Dashboard',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: primaryColor,
          brightness: Brightness.light,
        ),

        scaffoldBackgroundColor: bgColor,
        textTheme: GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme),
      ),
      home: const DashboardScreen(),
    );
  }
}
