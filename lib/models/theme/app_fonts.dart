import 'package:flutter/widgets.dart';
import 'package:google_fonts/google_fonts.dart';

class AppFonts {
  AppFonts._();

  static TextStyle boldTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w700);

  static TextStyle semiBoldTextStyle(double fontSize, {Color? color}) =>
      TextStyle(
          fontFamily: GoogleFonts.poppins().fontFamily,
          fontSize: fontSize,
          color: color,
          fontWeight: FontWeight.w600,
          height: 1.0);

  static TextStyle mediumTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w500);

  static TextStyle normalTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w400);
}
