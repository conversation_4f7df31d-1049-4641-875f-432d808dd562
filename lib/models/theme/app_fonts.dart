import 'package:flutter/widgets.dart';

class AppFonts {
  AppFonts._();

  static TextStyle boldTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w700);

  static TextStyle semiBoldTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w600);

  static TextStyle mediumTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w500);

  static TextStyle normalTextStyle(double fontSize, {Color? color}) =>
      TextStyle(fontSize: fontSize, color: color, fontWeight: FontWeight.w400);
}
