import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/constants.dart';

class SalesByBrokersCard extends StatelessWidget {
  const SalesByBrokersCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                "Sales by Brokers",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: const [
                    Text(
                      "Select Month",
                      style: TextStyle(
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 5),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 10,
              vertical: 5,
            ),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Text(
                  "Select Broker 1",
                  style: TextStyle(
                    fontSize: 12,
                  ),
                ),
                SizedBox(width: 5),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                ),
              ],
            ),
          ),
          const SizedBox(height: defaultPadding),
          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  height: 200,
                  width: 200,
                  child: CustomPaint(
                    painter: DonutChartPainter(),
                  ),
                ),
                Column(
                  children: const [
                    Text(
                      "1688",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      "Sales",
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: defaultPadding),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildAgentSalesItem(
                "Agent 1",
                "62",
                "25%",
                Colors.blue,
              ),
              _buildAgentSalesItem(
                "Agent 2",
                "55",
                "20%",
                Colors.green,
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          Center(
            child: TextButton.icon(
              onPressed: () {},
              icon: const Icon(
                Icons.visibility,
                color: Colors.blue,
                size: 16,
              ),
              label: const Text(
                "View More",
                style: TextStyle(
                  color: Colors.blue,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgentSalesItem(
      String name, String sales, String percentage, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 5),
            Text(
              percentage,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        Text(
          name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          "$sales Sales",
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}

class DonutChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final rect = Rect.fromCircle(center: center, radius: radius);

    // Define the segments
    final segments = [
      _ChartSegment(0.25, Colors.blue), // 25%
      _ChartSegment(0.20, Colors.green), // 20%
      _ChartSegment(0.15, Colors.orange), // 15%
      _ChartSegment(0.15, Colors.purple), // 15%
      _ChartSegment(0.25, Colors.grey.shade400), // 25%
    ];

    double startAngle = -90 * (3.14159 / 180); // Start from top (in radians)

    // Draw each segment
    for (var segment in segments) {
      final sweepAngle = segment.percentage * 2 * 3.14159; // Convert to radians
      final paint = Paint()
        ..color = segment.color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 20;

      canvas.drawArc(rect, startAngle, sweepAngle, false, paint);
      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _ChartSegment {
  final double percentage;
  final Color color;

  _ChartSegment(this.percentage, this.color);
}