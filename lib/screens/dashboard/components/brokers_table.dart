import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/app_theme.dart';
import 'package:neorevv_demo/config/constants.dart';
import 'package:neorevv_demo/config/responsive.dart';
import 'package:neorevv_demo/models/theme/app_fonts.dart';
import 'package:neorevv_demo/screens/dashboard/components/rounderd_icon_btn.dart';

class BrokersTable extends StatelessWidget {
  final int tableSize = 5;
  const BrokersTable({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(
        Responsive.isMobile(context) ? defaultPadding / 2 : defaultPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: defaultPadding),
          _buildTable(context),
          const SizedBox(height: defaultPadding),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(icon: 'user', onPressed: () {}),
              const SizedBox(width: 10),
              const Expanded(
                child: Text(
                  "Brokers",
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: TextField(
              decoration: InputDecoration(
                hintText: "Search by name",
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Image.asset('$iconAssetpath/user.png', height: 20, width: 20),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.only(top: 2),
          child: Text("Brokers", style: AppFonts.semiBoldTextStyle(22)),
        ),
        const Spacer(),
        SizedBox(
          width: Responsive.isTablet(context) ? 150 : 200,
          child: TextField(
            decoration: InputDecoration(
              hintText: "Search by name",
              hintStyle: AppFonts.normalTextStyle(
                14,
                color: AppTheme.primaryTextColor,
              ),
              prefixIcon: Container(
                height: 24,
                width: 24,
                padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                child: Image.asset('$iconAssetpath/search.png'),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppTheme.searchbarBg,
              contentPadding: const EdgeInsets.symmetric(vertical: 0),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTable(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(context);
    } else {
      return _buildDesktopTable(context);
    }
  }

  Widget _buildDesktopTable(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width - 2 * defaultPadding,
        ),
        child: DataTable(
          columnSpacing: defaultPadding * 0.8,
          dataRowMinHeight: 56,
          dataRowMaxHeight: 72,
          columns: [
            _dataColumn(name: 'Broker Name'),
            _dataColumn(name: 'Contacts'),
            _dataColumn(name: 'Email Address'),
            _dataColumn(name: 'Total Agents'),
            _dataColumn(name: 'Total Sales'),
            _dataColumn(name: 'Actions', allowSort: false),
          ],
          rows: List.generate(
            tableSize,
            (index) => _brokerDataRow(context, index),
          ),
        ),
      ),
    );
  }

  DataColumn _dataColumn({String name = '', bool allowSort = true}) {
    return DataColumn(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (name != '')
            Text(
              name,
              style: AppFonts.mediumTextStyle(
                14,
                color: AppTheme.tableColumnHeaderColor,
              ),
            ),
          SizedBox(width: name != '' ? 4 : 0),
          if (allowSort)
            Image.asset(
              '$iconAssetpath/column_sort.png',
              height: 16,
              width: 16,
            ),
        ],
      ),
    );
  }

  Widget _buildTabletTable(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width - 2 * defaultPadding,
        ),
        child: DataTable(
          columnSpacing: defaultPadding * 0.6,
          dataRowMinHeight: 56,
          dataRowMaxHeight: 72,
          columns: [
              _dataColumn(name: 'Broker Name'),
            _dataColumn(name: 'Contacts'),
            _dataColumn(name: 'Email Address'),
            _dataColumn(name: 'Total Agents'),
            _dataColumn(name: 'Total Sales'),alse),
          ],
          rows: List.generate(
            tableSize,
            (index) => _brokerDataRowTablet(context, index),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileTable(BuildContext context) {
    return Column(
      children: List.generate(
        tableSize,
        (index) => _buildMobileCard(context, index),
      ),
    );
  }

  Widget _buildMobileCard(BuildContext context, int index) {
    final brokers = [
      "Jane Cooper",
      "Floyd Miles",
      "Ronald Richards",
      "Marvin McKinney",
      "Jerome Bell",
    ];

    final agents = ["Agent 1", "Agent 2", "Agent 1", "Agent 3", "Agent 1"];

    return Container(
      margin: const EdgeInsets.only(bottom: defaultPadding),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(icon: 'eye', onPressed: () {}),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  brokers[index],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const CircleAvatar(
                radius: 12,
                backgroundColor: Colors.blue,
                child: Icon(Icons.person, color: Colors.white, size: 12),
              ),
              const SizedBox(width: 8),
              Text(agents[index]),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Email Address",
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  Text(
                    "${(index + 2) % 5 + 2}%",
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    "Revenue",
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const Text(
                    "\$5,447.00",
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.visibility, color: Colors.blue, size: 16),
              label: const Text(
                "View documents",
                style: TextStyle(color: Colors.blue, fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DataRow _brokerDataRow(BuildContext context, int index) {
    final brokers = [
      "Jane Cooper",
      "Floyd Miles",
      "Ronald Richards",
      "Marvin McKinney",
      "Jerome Bell",
    ];

    final agents = ["Agent 1", "Agent 2", "Agent 1", "Agent 3", "Agent 1"];

    return DataRow(
      cells: [
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              RoundIconBtn(icon: 'eye', onPressed: () {}),
              const SizedBox(width: 8),
              Flexible(
                child: Text(brokers[index], overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircleAvatar(
                radius: 12,
                backgroundColor: Colors.blue,
                child: Icon(Icons.person, color: Colors.white, size: 12),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(agents[index], overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
        ),
        const DataCell(Text("(*************", overflow: TextOverflow.ellipsis)),
        DataCell(
          Text("${(index + 2) % 5 + 2}%", overflow: TextOverflow.ellipsis),
        ),
        const DataCell(Text("\$5,447.00", overflow: TextOverflow.ellipsis)),
        DataCell(
          SizedBox(
            width: 120,
            child: TextButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.visibility, color: Colors.blue, size: 14),
              label: const Text(
                "View docs",
                style: TextStyle(color: Colors.blue, fontSize: 11),
              ),
            ),
          ),
        ),
      ],
    );
  }

  DataRow _brokerDataRowTablet(BuildContext context, int index) {
    final brokers = [
      "Jane Cooper",
      "Floyd Miles",
      "Ronald Richards",
      "Marvin McKinney",
      "Jerome Bell",
    ];

    final agents = ["Agent 1", "Agent 2", "Agent 1", "Agent 3", "Agent 1"];

    return DataRow(
      cells: [
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              RoundIconBtn(icon: 'eye', onPressed: () {}),
              const SizedBox(width: 6),
              Flexible(
                child: Text(brokers[index], overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircleAvatar(
                radius: 10,
                backgroundColor: Colors.blue,
                child: Icon(Icons.person, color: Colors.white, size: 10),
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(agents[index], overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
        ),
        DataCell(
          Text("${(index + 2) % 5 + 2}%", overflow: TextOverflow.ellipsis),
        ),
        const DataCell(Text("\$5,447.00", overflow: TextOverflow.ellipsis)),
        DataCell(
          SizedBox(
            width: 40,
            child: IconButton(
              onPressed: () {},
              icon: const Icon(Icons.visibility, color: Colors.blue, size: 14),
              tooltip: "View documents",
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Showing data 1 to 5 of 255 entries",
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: defaultPadding),
          _buildPagination(context),
        ],
      );
    }

    return Row(
      children: [
        Text(
          "Showing data 1 to 5 of 255 entries",
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const Spacer(),
        _buildPagination(context),
      ],
    );
  }

  Widget _buildPagination(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _paginationButton(icon: Icons.chevron_left),
          _paginationButton(label: "1", isSelected: true),
          _paginationButton(label: "2"),
          _paginationButton(label: "3"),
          _paginationButton(icon: Icons.chevron_right),
        ],
      );
    }

    return Row(
      children: [
        _paginationButton(icon: Icons.chevron_left),
        _paginationButton(label: "1", isSelected: true),
        _paginationButton(label: "2"),
        _paginationButton(label: "3"),
        _paginationButton(label: "4"),
        _paginationButton(label: "..."),
        _paginationButton(label: "40"),
        _paginationButton(icon: Icons.chevron_right),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: isSelected ? primaryColor : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Center(
        child: icon != null
            ? Icon(
                icon,
                size: 16,
                color: isSelected ? Colors.white : Colors.black,
              )
            : Text(
                label!,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontSize: 12,
                ),
              ),
      ),
    );
  }
}
