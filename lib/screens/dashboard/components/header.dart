import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/app_theme.dart';
import 'package:neorevv_demo/config/constants.dart';
import 'package:neorevv_demo/config/responsive.dart';

class Header extends StatelessWidget {
  const Header({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding / 2,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            spreadRadius: 0,
            blurRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (Responsive.isMobile(context))
            IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            ),
          Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),
          const SizedBox(width: 20),
          _buildNavItem(context, "Dashboard", isSelected: true),
          _buildNavItem(context, "Brokers"),
          _buildNavItem(context, "Agents"),
          _buildNavItem(context, "Sales"),
          _buildNavItem(context, "Commission"),
          const Spacer(),
          if (!Responsive.isMobile(context))
            ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: defaultPadding,
                  vertical: defaultPadding / 2,
                ),
              ),
              onPressed: () {},
              icon: const Icon(Icons.add),
              label: const Text("Add New"),
            ),
          const SizedBox(width: defaultPadding),
          _headerIcon(Icons.notifications_outlined),
          const SizedBox(width: defaultPadding),
          _headerIcon(Icons.settings_outlined),
          const SizedBox(width: defaultPadding),
          _buildProfileInfo(context),
        ],
      ),
    );
  }

  Container _headerIcon(IconData icon) {
    return Container(
      //background color-light grey, circle shape
      decoration: BoxDecoration(
        color: AppTheme.headerIconBgColor,
        shape: BoxShape.circle,
      ),

      child: Padding(padding: const EdgeInsets.all(5.0), child: Icon(icon)),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
  }) {
    return Visibility(
      visible: !Responsive.isMobile(context),
      child: Container(
        decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(
            //     color: isSelected ? primaryColor : Colors.transparent,
            //     width: 2,
            //   ),
            // ),
            ),
        padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                color: isSelected ? primaryColor : Colors.black,
                fontWeight: FontWeight.normal,
              ),
            ),
            // if (isSelected)
            //   Container(
            //     // margin: const EdgeInsets.only(top: 5),
            //     height: 3,
            //     width: 20,
            //     decoration: BoxDecoration(
            //       color: primaryColor,
            //       borderRadius: BorderRadius.circular(10),
            //     ),
            //   ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        const CircleAvatar(
          backgroundImage: AssetImage("$imageAssetpath/profile.png"),
        ),
        const SizedBox(width: defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text(
                "Robert Davidson",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text("Platform Owner", style: TextStyle(fontSize: 12)),
            ],
          ),
        if (Responsive.isDesktop(context))
          const Icon(Icons.keyboard_arrow_down),
      ],
    );
  }
}
