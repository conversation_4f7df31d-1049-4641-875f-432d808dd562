import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/constants.dart';
import 'package:neorevv_demo/config/responsive.dart';
import 'package:neorevv_demo/screens/dashboard/components/info_card_widget.dart';
import 'package:neorevv_demo/screens/dashboard/components/brokers_table.dart';
import 'package:neorevv_demo/screens/dashboard/components/agents_table.dart';
import 'package:neorevv_demo/screens/dashboard/components/commission_card.dart';
import 'package:neorevv_demo/screens/dashboard/components/sales_by_brokers_card.dart';

import '../../../config/app_theme.dart';
import '../../../config/json_consts.dart';

class DashboardContent extends StatelessWidget {
  const DashboardContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 5,
          child: Column(
            children: [
              _welcomeUser(),
              SizedBox(height: defaultPadding),
              infocardWidget(context),
              Sized<PERSON>ox(height: defaultPadding),
              BrokersTable(),
              if (Responsive.isMobile(context))
                SizedBox(height: defaultPadding),
              if (Responsive.isMobile(context) ||
                  _size.width < commisionCardBreakPoint)
                CommissionCard(),
            ],
          ),
        ),
        if (!Responsive.isMobile(context)) SizedBox(width: defaultPadding),
        // On Mobile means if the screen is less than 850 we don't want to show it
        if (!Responsive.isMobile(context) &&
            _size.width > commisionCardBreakPoint)
          Container(width: _size.width * 0.2, child: CommissionCard()),
      ],
    );
  }

  Widget _welcomeUser() {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.left,
        text: TextSpan(
          text: 'Welcome, ',
          style: TextStyle(
            color: AppTheme.primaryTextColor.withOpacity(0.7),
            fontSize: 23,
            fontWeight: FontWeight.w300,
          ),
          children: const [
            TextSpan(
              text: 'Platform Owner',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget infocardWidget(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    print(" _size.width:------->${_size.width}");
    return Responsive(
      mobile: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: _size.width < 700 && _size.width > 350 ? 1.8 : 2,
      ),
      tablet: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: _size.width < 950
            ? 2.5
            : _size.width < 1200 && _size.width > 700
            ? 2.5
            : 2.2,
      ),
      desktop: InfoCardGridView(
        childAspectRatio: _size.width < 1400 ? 1.3 : 2.1,
      ),
    );

    return Responsive(
      mobile: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InfoCardGridView(
            crossAxisCount: _size.width < 650 ? 2 : 4,
            childAspectRatio: _size.width < 650 && _size.width > 350 ? 1.3 : 1,
          ),
          SizedBox(height: defaultPadding),
          BrokersTable(),
          SizedBox(height: defaultPadding),
          CommissionCard(),
          SizedBox(height: defaultPadding),
          AgentsTable(),
          SizedBox(height: defaultPadding),
          SalesByBrokersCard(),
        ],
      ),
      tablet: Column(
        children: [
          InfoCardGridView(),
          SizedBox(height: defaultPadding),
          BrokersTable(),
          SizedBox(height: defaultPadding),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: CommissionCard()),
              SizedBox(width: defaultPadding),
              Expanded(child: SalesByBrokersCard()),
            ],
          ),
          SizedBox(height: defaultPadding),
          AgentsTable(),
        ],
      ),
      desktop: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _welcomeUser(),
                SizedBox(height: defaultPadding),
                InfoCardGridView(
                  childAspectRatio: _size.width < 1400 ? 2 : 1.5,
                ),
                SizedBox(height: defaultPadding),
                BrokersTable(),
                SizedBox(width: defaultPadding),
              ],
            ),
          ),
          SizedBox(width: defaultMargin),
          Expanded(flex: 1, child: CommissionCard()),
          // const SizedBox(height: defaultPadding),
          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: const [
          //     Expanded(flex: 3, child: AgentsTable()),
          //     SizedBox(width: defaultPadding),
          //     Expanded(flex: 2, child: SalesByBrokersCard()),
          //   ],
          // ),
        ],
      ),
    );
  }
}

class InfoCardGridView extends StatelessWidget {
  const InfoCardGridView({
    Key? key,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1,
  }) : super(key: key);

  final int crossAxisCount;
  final double childAspectRatio;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;

    return GridView.builder(
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: infoCards.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: defaultPadding,
        mainAxisSpacing: defaultPadding,
        childAspectRatio: childAspectRatio,
      ),
      itemBuilder: (context, index) => InfoCardWidget(
        title: infoCards[index].title,
        value: infoCards[index].value,
        icon: infoCards[index].assetImage,
        iconColor: infoCards[index].iconColor,
        subtitle: infoCards[index].subtitle,
        additionalInfo: infoCards[index].additionalInfo,
      ),
    );
  }
}

class Footer extends StatelessWidget {
  const Footer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Copyright © 2025 NeoRevv",
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(width: defaultPadding),
          TextButton(onPressed: () {}, child: const Text("Home")),
          const SizedBox(width: defaultPadding),
          TextButton(onPressed: () {}, child: const Text("Privacy Policy")),
          const SizedBox(width: defaultPadding),
          TextButton(
            onPressed: () {},
            child: const Text("Term and conditions"),
          ),
        ],
      ),
    );
  }
}
