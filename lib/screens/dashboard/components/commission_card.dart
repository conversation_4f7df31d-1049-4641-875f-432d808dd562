import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/app_theme.dart';
import 'package:neorevv_demo/config/constants.dart';
import 'package:neorevv_demo/config/responsive.dart';
import 'package:neorevv_demo/models/theme/app_fonts.dart';

import '../../../config/json_consts.dart';

class CommissionCard extends StatelessWidget {
  const CommissionCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commisionCardBreakPoint;

    return Container(
      constraints: BoxConstraints(maxHeight: size.height * 0.65),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: _buildBoxDecoration(),
      child: SingleChildScrollView(
        primary: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(isSmallView, size),
            const SizedBox(height: defaultPadding),
            _buildSelectionRow(context, isSmallView),
            const SizedBox(height: defaultPadding),
            ..._buildAgentCommissions(isSmallView),
            const Divider(color: Colors.white30),
            _buildTotalRow(),
            const SizedBox(height: defaultPadding),
            _buildViewMoreButton(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.comminsionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          "Gross Commission",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (!isSmallView)
          _buildDropdownChip(size.width < 1800 ? 'Month' : "Select Month"),
      ],
    );
  }

  Widget _buildSelectionRow(BuildContext context, bool isSmallView) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildDropdownChip(
            isSmallView ? "Broker1" : "Select Broker 1",
            expandText: true,
          ),
        ),
        if (!Responsive.isMobile(context) && isSmallView)
          const SizedBox(width: 8),
        if (!Responsive.isMobile(context) && isSmallView)
          Expanded(child: _buildDropdownChip("Month", expandText: true)),
      ],
    );
  }

  Widget _buildDropdownChip(String label, {bool expandText = false}) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: expandText ? MainAxisSize.max : MainAxisSize.min,
        children: [
          if (expandText)
            Expanded(
              child: Text(
                label,
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            )
          else
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          const SizedBox(width: 5),
          const Icon(Icons.keyboard_arrow_down, color: Colors.white, size: 16),
        ],
      ),
    );
  }

  List<Widget> _buildAgentCommissions(bool isSmallView) {
    return agents
        .map(
          (e) => Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: _buildAgentCommissionItem(
              e[0],
              e[1],
              e[2],
              agents.indexOf(e) % 2 == 0 ? true : false,
              isSmallView: isSmallView,
            ),
          ),
        )
        .toList();
  }

  Widget _buildAgentCommissionItem(
    String name,
    String sales,
    String amount,
    bool isDarkTheme, {
    required bool isSmallView,
  }) {
    final double imageSize = isSmallView ? 30 : 39;
    final double agentFontSize = isSmallView ? 14 : 16;
    final double saleFontSize = isSmallView ? 12 : 14;
    final double amountFontSize = isSmallView ? 18 : 20;
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      decoration: BoxDecoration(
        color: isDarkTheme
            ? AppTheme.comminsionCardDarkColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            '$iconAssetpath/commission_agent.png',
            height: imageSize,
            width: imageSize,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppFonts.mediumTextStyle(
                    agentFontSize,
                    color: Colors.white,
                  ),
                ),
                Text(
                  sales,
                  style: AppFonts.mediumTextStyle(
                    saleFontSize,
                    color: AppTheme.comminsionSalesTextColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 5),
          Text(
            amount,
            style: AppFonts.semiBoldTextStyle(
              amountFontSize,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: const [
        Text(
          "Total",
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          "\$98200",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildViewMoreButton() {
    return Center(
      child: TextButton.icon(
        onPressed: () {},
        icon: const Icon(Icons.visibility, color: Colors.white, size: 16),
        label: const Text("View More", style: TextStyle(color: Colors.white)),
      ),
    );
  }
}
