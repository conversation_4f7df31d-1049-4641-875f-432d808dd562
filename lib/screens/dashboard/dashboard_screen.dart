import 'package:flutter/material.dart';
import 'package:neorevv_demo/config/constants.dart';
import 'package:neorevv_demo/config/responsive.dart';
import 'package:neorevv_demo/screens/dashboard/components/header.dart';
import 'package:neorevv_demo/screens/dashboard/components/side_menu.dart';
import 'package:neorevv_demo/screens/dashboard/components/dashboard_content.dart';

import '../../config/app_theme.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      body: SafeArea(
        child: SingleChildScrollView(
          primary: false,
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Header(),
              Sized<PERSON>ox(height: defaultPadding),
              // Expanded(
              //   child: Padding(
              //     padding: const EdgeInsets.symmetric(vertical: defaultPadding),
              //     child: DashboardContent(),
              //   ),
              // ),
              DashboardContent(),
              // Footer
              // const SizedBox(height: defaultPadding),
              // const Footer(),
            ],
          ),
        ),
      ),
    );
  }
}
