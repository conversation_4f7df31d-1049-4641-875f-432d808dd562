import 'package:flutter/widgets.dart';

class AppTheme {
  const AppTheme._();

  static const Color primaryColor = Color(0xFF2697FF);
  static const Color secondaryColor = Color(0xFF2A2D3E);
  static const Color bgColor = Color(0xFFF0F6FF);
  static const Color blueCardColor = Color(0xFF0047AB);

  static const Color primaryTextColor = Color(0xFF333333);

  // Header
  static const Color headerIconBgColor = Color(0xFFE4EBF3);

  static const Color borderColor = Color(0xFFECEBEF);
  static const Color loginBgColor = Color(0xFF1684FF);

  static const Color comminsionCardColor = Color(0xFF00409D);
  static const Color comminsionDropDownBgColor = Color(0xFF0259D8);
  static const Color comminsionCardDarkColor = Color(0xFF002F7A);
  static const Color comminsionSalesTextColor = Color(0xFF90B5EA);

  // Table
  static const Color paginationActiveBg = Color(0xFF28569F);
  static const Color paginationInactiveBg = Color(0xFFF5F5F5);
  static const Color searchbarBg = Color(0xFFE4EBF3);
  static const Color pageSummaryLabelColor = Color(0xFFB5B7C0);
  static const Color tableColumnHeaderColor = Color(0xFF7C7E89);

  //icon
  static const Color roundIconBgColor = Color(0xFFEEEEEE);
  static const Color roundIconColor = Color(0xFF1A71DB);
}
