import 'package:flutter/material.dart';

import '../models/info_card.dart';
import 'constants.dart';

final List<InfoCardData> infoCards = [
  InfoCardData(
    title: "Total Brokers",
    value: "168",
    assetImage: '$iconAssetpath/brokers.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Total Sales",
    value: "20K",
    assetImage: '$iconAssetpath/sales.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Revenue",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

final agents = [
  ["Agent 1 long name test", "12 Sales", "\$2200"],
  ["Agent 2", "30 Sales", "\$2600"],
  ["Agent 3", "8 Sales", "\$1200"],
  ["Agent 4", "25 Sales", "\$900"],
  ["Agent 5", "13 Sales", "\$3800"],
  ["Agent 6", "10 Sales", "\$800"],
  ["Agent 7", "30 Sales", "\$5820"],
  ["Agent 4", "25 Sales", "\$900"],
  ["Agent 5", "13 Sales", "\$3800"],
  ["Agent 6", "10 Sales", "\$800"],
  ["Agent 7", "30 Sales", "\$5820"],
];
